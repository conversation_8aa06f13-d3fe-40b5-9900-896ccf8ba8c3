import asyncio
import aiohttp
import aiofiles
import os
import tiktoken
import time
from datetime import datetime, timedelta
import tkinter as tk
from tkinter import filedialog
import random

# 双API Key配置
API_KEYS = [
    'sk-oyCIv8iRpgC265xq24396dB6AbEc4e908342F8876fFe389d',
    'sk-toCwQl8ObRPPSSB798751742EaF844658e83Aa9207A47c65'
]
BASE_URL = "https://free.v36.cm/v1/"

# 系统提示词  
SYSTEM_PROMPT = """You are a Midjourney prompt optimizer, tasked with making creatively rich modifications to prompts while ensuring quality is maintained.

Rules:

Replace the main subject with a creative alternative (keeping the same style/mood), and at the same time, creatively adjust more descriptive elements such as actions, scene layouts, decorative details, etc., to increase the range of modifications.
Keep all original parameters (--ar, --v, etc.) exactly unchanged.
Retain core detailed descriptions (lighting, textures, colors, materials, style, etc.), allowing for expansive modifications to non-core details.
Ensure "on white background" is included (add if missing).
Output only a single line, with no explanations.

Examples:
Input: "a cute cat sitting, soft lighting --ar 1:1"
Output: "a chubby rabbit hugging a carrot, curled up on a cushion, soft lighting on white background --ar 1:1"

Input: "ornate castle with rich architectural details, fantasy style --v 5 --ar 2:3"
Output: "ornate floating palace adorned with glowing vines, architectural details as intricate as a maze, fantasy style on white background --v 5 --ar 2:3"

Transform the following prompt:"""

# 全局变量用于统计
processed_count = 0
success_count = 0
error_count = 0
skipped_count = 0  # 跳过的垃圾内容数量
total_input_tokens = 0
total_output_tokens = 0
total_cost = 0.0
start_time = None
total_prompts = 0

# 添加一个全局锁来确保写入的原子性
import asyncio
write_lock = asyncio.Lock()

# 炼丹版特有统计
total_rounds = 3  # 总共处理3轮
current_round = 0  # 当前轮次
round_stats = {1: {'success': 0, 'error': 0, 'skipped': 0}, 
               2: {'success': 0, 'error': 0, 'skipped': 0}, 
               3: {'success': 0, 'error': 0, 'skipped': 0}}

# 双key速率限制配置
MAX_REQUESTS_PER_MINUTE_PER_KEY = 96
key_request_times = {0: [], 1: []}  # 为每个key单独记录请求时间
key_usage_count = {0: 0, 1: 0}  # 记录每个key的使用次数

# 控制台输出优化变量
last_rate_limit_message = {}  # 记录上次速率限制消息
rate_limit_count = {0: 0, 1: 0}  # 记录连续速率限制次数
last_progress_time = 0  # 上次显示进度的时间
current_rate_limit_status = {}  # 当前速率限制状态

# 价格配置（每1000个token的价格）
INPUT_PRICE_PER_1K = 0.00015  # $0.00015 / 1k tokens
OUTPUT_PRICE_PER_1K = 0.0006  # $0.0006 / 1k tokens

# 初始化tokenizer
try:
    tokenizer = tiktoken.encoding_for_model("gpt-4o-mini")
except:
    tokenizer = tiktoken.get_encoding("cl100k_base")

def count_tokens(text):
    """计算文本的token数量"""
    return len(tokenizer.encode(text))

def calculate_cost(input_tokens, output_tokens):
    """计算API调用成本"""
    input_cost = (input_tokens / 1000) * INPUT_PRICE_PER_1K
    output_cost = (output_tokens / 1000) * OUTPUT_PRICE_PER_1K
    return input_cost + output_cost

def contains_chinese(text):
    """检测文本是否包含中文字符"""
    import re
    # 匹配中文字符的正则表达式
    chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
    return bool(chinese_pattern.search(text))

def is_valid_input_line(line):
    """检测输入行是否有效（用于读取txt时过滤）"""
    if not line or not isinstance(line, str):
        return False

    # 清理空白字符
    import re
    cleaned_line = re.sub(r'\s+', ' ', line).strip()

    # 检查长度，少于100字符的不要
    if len(cleaned_line) < 100:
        return False

    # 检查是否包含中文，包含中文的不要
    if contains_chinese(cleaned_line):
        return False

    return True

def is_valid_result(result):
    """检测生成结果是否有效（用于写入txt时过滤）"""
    if not result or not isinstance(result, str):
        return False

    # 彻底清理所有空白字符（包括换行符、制表符等）
    import re
    cleaned_result = re.sub(r'\s+', ' ', result).strip()

    # 检查长度，少于100字符的内容不要
    if len(cleaned_result) < 100:
        return False

    # 检查是否包含中文，包含中文的不要
    if contains_chinese(cleaned_result):
        return False

    # 额外检查：如果结果看起来像是被截断的（以常见的不完整模式开头）
    if cleaned_result.startswith(('style --', '--ar', '--v', 'background --', 'lighting --')):
        return False

    return True

def format_time(seconds):
    """格式化时间显示"""
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = seconds // 60
        secs = seconds % 60
        return f"{int(minutes)}分{int(secs)}秒"
    else:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        secs = seconds % 60
        return f"{int(hours)}小时{int(minutes)}分{int(secs)}秒"

def display_progress_stats(force=False):
    """显示进度统计信息（炼丹版）"""
    global start_time, processed_count, success_count, error_count, skipped_count, total_cost, total_prompts, last_progress_time
    global total_input_tokens, total_output_tokens, current_round

    if start_time is None:
        return

    current_time = time.time()
    # 只有强制显示或者距离上次显示超过3秒才显示
    if not force and current_time - last_progress_time < 3:
        return

    last_progress_time = current_time
    elapsed_time = current_time - start_time

    # 修正：总体进度应该基于原始提示词数量，而不是3倍
    total_expected = total_prompts  # 每个原始提示词最终产生1个结果
    total_processed = processed_count
    progress_percent = (total_processed / total_expected) * 100 if total_expected > 0 else 0

    # 计算处理速度（每分钟）
    speed_per_minute = (total_processed / elapsed_time) * 60 if elapsed_time > 0 else 0

    # 剩余时间估算
    if total_processed > 0 and total_processed < total_expected:
        remaining_time = ((total_expected - total_processed) / total_processed) * elapsed_time
        remaining_str = format_time(remaining_time)
    else:
        remaining_str = "完成"

    # 创建简洁的进度条
    bar_length = 20
    filled_length = int(bar_length * progress_percent / 100)
    bar = '█' * filled_length + '░' * (bar_length - filled_length)

    # 格式化token显示
    input_k = total_input_tokens / 1000
    output_k = total_output_tokens / 1000

    # 显示跳过数量（如果有的话）
    skip_info = f" | 跳过:{skipped_count}" if skipped_count > 0 else ""

    print(f"\r[{bar}] {progress_percent:.1f}% | "
          f"处理:{total_processed}/{total_expected} | "
          f"成功:{success_count} | 失败:{error_count}{skip_info} | "
          f"速度:{speed_per_minute:.0f}/分 | "
          f"输入:{input_k:.1f}K | 输出:{output_k:.1f}K | "
          f"成本:${total_cost:.4f} | "
          f"剩余:{remaining_str}", end="", flush=True)

def select_input_file():
    """选择输入文件"""
    # 创建隐藏的根窗口
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    root.attributes('-topmost', True)  # 设置置顶
    
    # 打开文件选择对话框
    input_file = filedialog.askopenfilename(
        title="选择提示词文件",
        filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
        parent=root
    )
    
    root.destroy()  # 销毁根窗口
    return input_file

def get_key_for_round(round_num):
    """根据轮次获取对应的key索引，确保每轮使用不同的key"""
    # 轮次1使用key0，轮次2使用key1，轮次3使用key0（循环使用）
    return (round_num - 1) % len(API_KEYS)

async def rate_limit_for_key(key_idx):
    """为特定key进行速率限制控制"""
    current_time = time.time()
    
    # 清理超过1分钟的请求记录
    key_request_times[key_idx] = [t for t in key_request_times[key_idx] if current_time - t < 60]
    
    # 如果请求数量达到限制，等待
    if len(key_request_times[key_idx]) >= MAX_REQUESTS_PER_MINUTE_PER_KEY:
        sleep_time = 60 - (current_time - key_request_times[key_idx][0])
        if sleep_time > 0:
            await asyncio.sleep(sleep_time)
            # 重新清理请求记录
            current_time = time.time()
            key_request_times[key_idx] = [t for t in key_request_times[key_idx] if current_time - t < 60]
    
    # 记录当前请求时间
    key_request_times[key_idx].append(current_time)
    key_usage_count[key_idx] += 1

async def process_single_round(session, prompt, key_idx, round_num):
    """处理单轮AI请求"""
    global total_input_tokens, total_output_tokens, total_cost
    
    # 对选定的key进行速率限制
    await rate_limit_for_key(key_idx)
    
    # 使用选定的API key
    headers = {
        "Authorization": f"Bearer {API_KEYS[key_idx]}",
        "Content-Type": "application/json",
    }
    
    # 计算输入token数量
    system_tokens = count_tokens(SYSTEM_PROMPT)
    user_tokens = count_tokens(prompt.strip())
    input_tokens = system_tokens + user_tokens
    
    async with session.post(
        url=f"{BASE_URL}chat/completions",
        json={
            "model": "gpt-4o-mini",
            "max_tokens": 4000,
            "temperature": 0.5,
            "messages": [
                {"role": "system", "content": SYSTEM_PROMPT},
                {"role": "user", "content": prompt.strip()}
            ],
        },
        headers=headers,
        timeout=aiohttp.ClientTimeout(total=30)  # 30秒超时
    ) as response:
        if response.status == 200:
            result = await response.json()
            ai_response = result['choices'][0]['message']['content']
            
            # 计算输出token数量
            output_tokens = count_tokens(ai_response)
            
            # 计算本次请求成本
            request_cost = calculate_cost(input_tokens, output_tokens)
            
            # 更新全局统计
            total_input_tokens += input_tokens
            total_output_tokens += output_tokens
            total_cost += request_cost
            
            return True, ai_response, f"轮次{round_num}成功使用Key{key_idx+1}"
        else:
            error_text = await response.text()
            return False, None, f"轮次{round_num}HTTP错误 {response.status}: {error_text}, 使用Key{key_idx+1}"

async def process_prompt_three_rounds(session, semaphore, original_prompt, output_file, prompt_index):
    """炼丹版：对单个提示词进行三轮AI处理"""
    global processed_count, success_count, error_count, skipped_count, current_round

    async with semaphore:
        current_prompt = original_prompt
        round_results = {}  # 仅记录每轮通过过滤的结果（不落盘）
        processing_log = []  # 记录每轮处理的日志

        # 进行三轮处理
        for round_num in range(1, total_rounds + 1):
            key_idx = get_key_for_round(round_num)

            max_retries = 2  # 每轮最多重试2次
            round_success = False

            for attempt in range(max_retries + 1):
                try:
                    # 处理当前轮次
                    success, result, message = await process_single_round(session, current_prompt, key_idx, round_num)

                    if success and is_valid_result(result):
                        # 本轮成功（通过过滤）
                        processing_log.append(f"轮次{round_num}: 成功 (Key{key_idx+1})")
                        round_results[round_num] = result  # 仅记录，不落盘
                        current_prompt = result  # 下一轮使用这个结果作为输入
                        round_stats[round_num]['success'] += 1
                        round_success = True
                        break
                    elif success and not is_valid_result(result):
                        # 结果无效（不过滤，不落盘，也不作为下一轮输入）
                        processing_log.append(f"轮次{round_num}: 结果无效 (Key{key_idx+1})")
                        round_stats[round_num]['skipped'] += 1
                        if attempt < max_retries:
                            await asyncio.sleep(min(2 ** attempt, 5))
                        else:
                            # 最后一次尝试也失败，继续下一轮（沿用上一轮有效输入）
                            break
                    else:
                        # 请求失败
                        processing_log.append(f"轮次{round_num}: 失败 - {message}")
                        if attempt < max_retries:
                            await asyncio.sleep(min(2 ** attempt, 5))
                        else:
                            round_stats[round_num]['error'] += 1
                            # 使用上一轮的结果继续（如果有的话）
                            break

                except asyncio.TimeoutError:
                    processing_log.append(f"轮次{round_num}: 超时 (Key{key_idx+1})")
                    if attempt < max_retries:
                        await asyncio.sleep(min(2 ** attempt, 5))
                    else:
                        round_stats[round_num]['error'] += 1
                        break

                except Exception as e:
                    processing_log.append(f"轮次{round_num}: 异常 - {e}")
                    if attempt < max_retries:
                        await asyncio.sleep(min(2 ** attempt, 5))
                    else:
                        round_stats[round_num]['error'] += 1
                        break

            # 如果本轮完全失败且之前没有任何有效结果，跳出循环
            if not round_success and not round_results:
                break

        # 使用全局锁确保写入的原子性，避免并发写入问题
        async with write_lock:
            # 仅保存第3轮的最终有效结果，禁止保存中间轮次
            final_output = round_results.get(3)

            async with aiofiles.open(output_file, 'a', encoding='utf-8') as f:
                if final_output and is_valid_result(final_output):
                    await f.write(f"{final_output}\n")
                    success_count += 1
                else:
                    # 如果第3轮失败，尝试使用第2轮或第1轮的结果
                    fallback_output = round_results.get(2) or round_results.get(1)
                    if fallback_output and is_valid_result(fallback_output):
                        await f.write(f"{fallback_output}\n")
                        success_count += 1
                        processing_log.append(f"使用轮次{2 if round_results.get(2) else 1}结果作为备选")
                    else:
                        # 完全失败，记录失败信息
                        log_str = " | ".join(processing_log)
                        await f.write(f"# 炼丹失败[提示词{prompt_index+1}]: {log_str}\n")
                        error_count += 1

            processed_count += 1

        # 每处理5个或每2秒显示一次进度统计
        if processed_count % 5 == 0:
            display_progress_stats(force=True)
        else:
            display_progress_stats()

async def main():
    """主程序（炼丹版）"""
    global processed_count, success_count, error_count, skipped_count, total_input_tokens, total_output_tokens, total_cost
    global start_time, total_prompts, current_round

    # 选择输入文件
    input_file = select_input_file()
    if not input_file:
        print("[错误] 未选择文件，程序退出！")
        return

    if not os.path.exists(input_file):
        print(f"[错误] 选择的文件 {input_file} 不存在！")
        return

    # 创建输出文件名（带时间戳）
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"AI炼丹结果_双key_{timestamp}.txt"

    # 读取所有提示词，过滤掉无效行
    async with aiofiles.open(input_file, 'r', encoding='utf-8') as f:
        content = await f.read()
        all_lines = [line.strip() for line in content.split('\n') if line.strip()]
        prompts = [line for line in all_lines if is_valid_input_line(line)]

        # 统计过滤情况
        filtered_count = len(all_lines) - len(prompts)
        if filtered_count > 0:
            print(f"[过滤] 已过滤掉 {filtered_count} 行无效内容（少于100字符或包含中文）")

    total_prompts = len(prompts)
    start_time = time.time()
    start_datetime = datetime.now()

    print("=" * 70)
    print("批量提示词处理程序 (双Key炼丹版)")
    print("=" * 70)
    print(f"文件: {os.path.basename(input_file)}")
    print(f"数量: {total_prompts} 个")
    print(f"炼丹: 每个提示词进行 {total_rounds} 轮AI处理")
    print(f"策略: 轮次1用Key1 → 轮次2用Key2 → 轮次3用Key1")
    print(f"开始: {start_datetime.strftime('%H:%M:%S')}")
    print(f"配置: 双Key轮换 | 最大{MAX_REQUESTS_PER_MINUTE_PER_KEY * 2}/分钟 | 15并发")
    print("=" * 70)

    # 创建输出文件
    async with aiofiles.open(output_file, 'w', encoding='utf-8') as f:
        await f.write("")  # 创建空文件

    # 创建信号量限制并发数为15（炼丹版需要更多资源，降低并发）
    semaphore = asyncio.Semaphore(15)

    # 创建HTTP会话
    async with aiohttp.ClientSession() as session:
        # 创建所有任务，添加索引以便调试
        tasks = [
            process_prompt_three_rounds(session, semaphore, prompt, output_file, index)
            for index, prompt in enumerate(prompts)
        ]

        # 执行所有任务
        await asyncio.gather(*tasks)

    # 计算总运行时长
    end_time = time.time()
    total_runtime = end_time - start_time
    end_datetime = datetime.now()

    print("\n" + "=" * 70)
    print("炼丹完成！")
    print("=" * 70)
    print(f"时间: {start_datetime.strftime('%H:%M:%S')} → {end_datetime.strftime('%H:%M:%S')} ({format_time(total_runtime)})")
    print(f"结果: {success_count}成功 / {error_count}失败 / {skipped_count}跳过 / {total_prompts}总计")

    # 安全计算有效率和速度，避免除零错误
    if total_prompts > 0:
        success_rate = (success_count/total_prompts)*100
        speed_per_minute = (total_prompts/total_runtime)*60 if total_runtime > 0 else 0
        print(f"有效率: {success_rate:.1f}% | 速度: {speed_per_minute:.0f}个/分钟")

        # 显示各轮次统计
        print(f"轮次统计:")
        for round_num in range(1, total_rounds + 1):
            stats = round_stats[round_num]
            print(f"  轮次{round_num}: 成功{stats['success']} / 失败{stats['error']} / 跳过{stats['skipped']}")

        print(f"Token: 输入{total_input_tokens:,} / 输出{total_output_tokens:,}")
        print(f"成本: ${total_cost:.6f}")
        print(f"Key使用: Key1={key_usage_count[0]} / Key2={key_usage_count[1]}")
        print("=" * 70)
        print(f"文件: {output_file}")
    else:
        print("⚠️  没有找到有效的提示词进行处理！")
        print("原因可能是：")
        print("  - 文件中的内容少于100字符")
        print("  - 文件中包含中文内容")
        print("  - 文件为空或格式不正确")
        print("请检查输入文件并重试。")

    print("=" * 70)

if __name__ == "__main__":
    asyncio.run(main())
